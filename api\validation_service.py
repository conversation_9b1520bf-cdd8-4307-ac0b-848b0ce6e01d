"""
API验证服务
提供OCR功能和弹窗监控功能的验证测试
"""

import os
import sys
import time
import tempfile
from datetime import datetime
from typing import Dict, Any, Optional
from PIL import Image, ImageDraw, ImageFont

from utils.logs.LoggerManager import get_logger


class ValidationService:
    """API验证服务，用于测试核心功能模块"""
    
    def __init__(self):
        """初始化验证服务"""
        self.logger = get_logger(__name__)
        self.logger.info("验证服务初始化完成")
    
    def validate_ocr_functionality(self, test_image_path: Optional[str] = None) -> Dict[str, Any]:
        """
        验证OCR功能
        
        Args:
            test_image_path: 可选的测试图片路径，如果不提供则创建测试图片
            
        Returns:
            Dict: 验证结果
        """
        result = {
            "module": "OCR",
            "status": "unknown",
            "message": "",
            "details": {},
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 导入OCR相关模块
            from utils.ocr import OCR
            from utils.ocr.ButtonCoordinate import get_base_path, preprocess_image
            
            result["details"]["imports"] = "success"
            
            # 检查OCR DLL和数据文件
            dll_status = self._check_ocr_dependencies()
            result["details"]["dependencies"] = dll_status
            
            if not dll_status["dll_exists"] or not dll_status["tessdata_exists"]:
                result["status"] = "error"
                result["message"] = "OCR依赖文件缺失"
                return result
            
            # 创建或使用测试图片
            if test_image_path and os.path.exists(test_image_path):
                image_path = test_image_path
                result["details"]["test_image"] = "provided"
            else:
                image_path = self._create_test_image()
                result["details"]["test_image"] = "generated"
            
            result["details"]["image_path"] = image_path
            
            # 测试OCR初始化
            try:
                dll_path = dll_status["dll_path"]
                tessdata_prefix = dll_status["tessdata_path"].encode('utf-8')
                lang = b'chi_sim'
                
                ocr = OCR.OCR(dll_path, tessdata_prefix, lang)
                result["details"]["ocr_init"] = "success"
                
                # 测试OCR文字识别
                if os.path.exists(image_path):
                    image_path_encoded = image_path.encode('utf-8')
                    ocr_result = ocr.get_text_and_positions(image_path_encoded)
                    
                    if ocr_result:
                        result["details"]["text_recognition"] = {
                            "success": True,
                            "text_count": len(ocr_result),
                            "sample_text": ocr_result[0].get("text", "") if ocr_result else ""
                        }
                        result["status"] = "success"
                        result["message"] = "OCR功能验证成功"
                    else:
                        result["details"]["text_recognition"] = {
                            "success": False,
                            "text_count": 0
                        }
                        result["status"] = "warning"
                        result["message"] = "OCR初始化成功但未识别到文字"
                else:
                    result["status"] = "error"
                    result["message"] = "测试图片不存在"
                    
            except Exception as ocr_error:
                result["details"]["ocr_error"] = str(ocr_error)
                result["status"] = "error"
                result["message"] = f"OCR初始化或识别失败: {str(ocr_error)}"
            
            # 清理临时文件
            if not test_image_path and os.path.exists(image_path):
                try:
                    os.remove(image_path)
                    result["details"]["cleanup"] = "success"
                except:
                    result["details"]["cleanup"] = "failed"
                    
        except ImportError as e:
            result["status"] = "error"
            result["message"] = f"OCR模块导入失败: {str(e)}"
            result["details"]["import_error"] = str(e)
        except Exception as e:
            result["status"] = "error"
            result["message"] = f"OCR验证过程出错: {str(e)}"
            result["details"]["general_error"] = str(e)
        
        return result
    
    def validate_popup_monitor_functionality(self) -> Dict[str, Any]:
        """
        验证弹窗监控功能
        
        Returns:
            Dict: 验证结果
        """
        result = {
            "module": "PopupMonitor",
            "status": "unknown",
            "message": "",
            "details": {},
            "timestamp": datetime.now().isoformat()
        }
        
        try:
            # 导入弹窗监控相关模块
            from utils.PopupMonitor import PopupMonitor
            from utils.PopupConfig import PopupConfig
            
            result["details"]["imports"] = "success"
            
            # 检查uiautomator2依赖
            u2_status = self._check_uiautomator2_dependency()
            result["details"]["uiautomator2"] = u2_status
            
            # 检查配置文件
            config_status = self._check_popup_config()
            result["details"]["config"] = config_status
            
            # 测试PopupConfig初始化
            try:
                popup_config = PopupConfig()
                result["details"]["popup_config_init"] = "success"

                # 获取配置信息
                result["details"]["config_data"] = {
                    "enabled": popup_config.is_enabled(),
                    "rules_count": len(popup_config.get_rules()),
                    "interval": popup_config.get_check_interval()
                }
                
            except Exception as config_error:
                result["details"]["popup_config_error"] = str(config_error)
                result["status"] = "error"
                result["message"] = f"PopupConfig初始化失败: {str(config_error)}"
                return result
            
            # 测试PopupMonitor初始化（不启动实际监控）
            try:
                popup_monitor = PopupMonitor(popup_config)
                result["details"]["popup_monitor_init"] = "success"
                
                # 检查监控器状态
                result["details"]["monitor_status"] = {
                    "is_running": popup_monitor.is_running(),
                    "has_config": popup_monitor.config is not None
                }
                
                result["status"] = "success"
                result["message"] = "弹窗监控功能验证成功"
                
            except Exception as monitor_error:
                result["details"]["popup_monitor_error"] = str(monitor_error)
                result["status"] = "error"
                result["message"] = f"PopupMonitor初始化失败: {str(monitor_error)}"
                
        except ImportError as e:
            result["status"] = "error"
            result["message"] = f"弹窗监控模块导入失败: {str(e)}"
            result["details"]["import_error"] = str(e)
        except Exception as e:
            result["status"] = "error"
            result["message"] = f"弹窗监控验证过程出错: {str(e)}"
            result["details"]["general_error"] = str(e)
        
        return result
    
    def _check_ocr_dependencies(self) -> Dict[str, Any]:
        """检查OCR依赖文件"""
        result = {
            "dll_exists": False,
            "tessdata_exists": False,
            "dll_path": "",
            "tessdata_path": ""
        }
        
        try:
            # 检查DLL文件
            dll_paths = ['./libtesseract304.dll', './_internal/libtesseract304.dll']
            for dll_path in dll_paths:
                if os.path.exists(dll_path):
                    result["dll_exists"] = True
                    result["dll_path"] = dll_path
                    break
            
            # 检查tessdata目录
            tessdata_paths = ['./utils/ocr/tessdata', './_internal/utils/ocr/tessdata']
            for tessdata_path in tessdata_paths:
                if os.path.exists(tessdata_path):
                    result["tessdata_exists"] = True
                    result["tessdata_path"] = tessdata_path
                    break
                    
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _check_uiautomator2_dependency(self) -> Dict[str, Any]:
        """检查uiautomator2依赖"""
        result = {
            "available": False,
            "version": "",
            "import_method": ""
        }
        
        try:
            import uiautomator2 as u2
            result["available"] = True
            result["import_method"] = "direct"
            try:
                result["version"] = u2.__version__
            except:
                result["version"] = "unknown"
        except ImportError:
            # 尝试备用导入方法
            try:
                import importlib.util
                if getattr(sys, 'frozen', False):
                    base_dir = os.path.dirname(sys.executable)
                    possible_paths = [
                        os.path.join(base_dir, "uiautomator2", "__init__.py"),
                        os.path.join(base_dir, "_internal", "uiautomator2", "__init__.py")
                    ]
                    
                    for path in possible_paths:
                        if os.path.exists(path):
                            result["available"] = True
                            result["import_method"] = "alternative"
                            break
            except:
                pass
        
        return result
    
    def _check_popup_config(self) -> Dict[str, Any]:
        """检查弹窗配置文件"""
        result = {
            "config_exists": False,
            "config_path": "",
            "valid_json": False
        }
        
        config_paths = ['popup_config.json', './popup_config.json', 'utils/popup_config.json']
        
        for config_path in config_paths:
            if os.path.exists(config_path):
                result["config_exists"] = True
                result["config_path"] = config_path
                
                # 检查JSON格式
                try:
                    import json
                    with open(config_path, 'r', encoding='utf-8') as f:
                        json.load(f)
                    result["valid_json"] = True
                except:
                    result["valid_json"] = False
                break
        
        return result
    
    def _create_test_image(self) -> str:
        """创建测试图片"""
        try:
            # 创建临时图片
            temp_dir = tempfile.gettempdir()
            image_path = os.path.join(temp_dir, f"ocr_test_{int(time.time())}.png")
            
            # 创建包含文字的测试图片
            img = Image.new('RGB', (400, 200), color='white')
            draw = ImageDraw.Draw(img)
            
            # 尝试使用默认字体
            try:
                # 在Windows上尝试使用系统字体
                if sys.platform == 'win32':
                    font = ImageFont.truetype("arial.ttf", 24)
                else:
                    font = ImageFont.load_default()
            except:
                font = ImageFont.load_default()
            
            # 绘制测试文字
            test_text = "测试文字 Test Text"
            draw.text((50, 80), test_text, fill='black', font=font)
            
            # 保存图片
            img.save(image_path)
            return image_path
            
        except Exception as e:
            self.logger.error(f"创建测试图片失败: {e}")
            return ""
