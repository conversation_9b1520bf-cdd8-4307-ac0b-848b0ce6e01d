# 预设任务队列顺序执行功能

## ⚠️ 文件修改/新增限定
仅允许修改/新增以下文件，其余文件保持不变，避免误改：
- `queues/TaskQueueManager.py` (新增)
- `controller/QueueController.py` (新增，如合并到 `MainController.py` 则只修改 `MainController.py`)
- `controller/MainController.py` (修改 – 添加"加入执行队列"逻辑)
- `controller/FourController.py` (修改 – 同上)
- `main.py` (修改 – 新增"任务队列"主面板及入口按钮)
- `requirements.txt` (仅当确有新增第三方依赖时才允许修改)
- `tests/test_task_queue_manager.py` (新增 – 单元测试)

## 目标与约束
1. 支持将任意"预设任务"加入一个全局执行队列，多个队列按加入顺序串行执行。
2. 执行过程单线程，最小化资源消耗；不考虑并发、重试、优先级等复杂特性。
3. UI 需在现有框架内新增"任务队列"主面板，展示队列列表、执行进度，并提供开始 / 暂停 / 取消按钮。
4. 保持现有日志格式，出错时记录并继续后续队列。
5. 严格限制文件改动范围（见上）。

## 详细任务分解
### 1 核心类 `TaskQueueManager`（queues/TaskQueueManager.py）
- [ ] `queues : List[TestQueue]` 保存待执行的预设队列
- [ ] `add_queue(test_queue: TestQueue)` 将现有队列加入待执行列表
- [ ] `execute_all(device_id: str, *args)` 依次调用每个 `TestQueue.start_queue`，同步阻塞式执行
- [ ] `clear()` 清空队列
- [ ] 日志记录：开始、结束、异常

### 2 控制器层
- [ ] 在 `controller/MainController.py` 与 `controller/FourController.py` 中，新增方法 `add_to_execution_queue()`：
  * 读取当前已选预设任务（现有逻辑中已可获取 `self.queue`）
  * 实例化 `TestQueue` 并调用 `TaskQueueManager.add_queue`
  * 给用户提示"已加入执行队列"
- [ ] 可选：独立 `controller/QueueController.py`，封装对 `TaskQueueManager` 的操作，供 UI 调用

### 3 UI 层（main.py）
- [ ] 在现有分页后新增 Tab/面板"任务队列"
  * 队列列表：`ListBox` 展示等待执行的队列标题
  * 控制按钮：开始执行 / 清空队列
  * 进度条：显示当前队列及整体进度
- [ ] 在各预设页面的"保存/加载/运行"按钮旁新增"加入执行队列"按钮，绑定到 `add_to_execution_queue()`

### 4 执行流程
- [ ] 点击"开始执行"后，UI 调用 `TaskQueueManager.execute_all(device_id)`：
  * 遍历队列 → 调用 `TestQueue.start_queue`
  * 将进度与日志反馈给 UI
  * 执行完毕后清空队列，提示用户

### 5 日志与异常处理
- [ ] 复用现有 `self.logs` 记录
- [ ] 对单个队列执行异常进行 `try/except` 捕获，记录并继续下一队列

### 6 测试（tests/test_task_queue_manager.py）
- [ ] 单元测试：`add_queue`、`clear`、执行顺序正确性（可用 `unittest.mock` 模拟 `TestQueue`）

### 7 文档
- [ ] `README.md` 更新：使用说明 & 注意事项

## 里程碑
- [ ] Milestone 1：完成 `TaskQueueManager` 类与单元测试
- [ ] Milestone 2：控制器层加入队列功能并通过集成测试
- [ ] Milestone 3：UI 层新面板与交互完成
- [ ] Milestone 4：整体联调，文档更新

---
完成以上里程碑后，即实现"预设任务队列顺序执行"功能。
