# 亮度降幅自动化程序 API 测试脚本
# PowerShell版本

param(
    [string]$BaseUrl = "http://127.0.0.1:9080/api",
    [switch]$Detailed = $false
)

# 设置控制台编码为UTF-8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

Write-Host "=" * 80 -ForegroundColor Cyan
Write-Host "亮度降幅自动化程序 API 测试" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Cyan
Write-Host "基础URL: $BaseUrl" -ForegroundColor Yellow
Write-Host "测试时间: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Yellow
Write-Host ""

function Test-ApiEndpoint {
    param(
        [string]$Url,
        [string]$Name,
        [string]$Method = "GET",
        [hashtable]$Body = $null
    )
    
    Write-Host "测试: $Name" -ForegroundColor Green
    Write-Host "URL: $Url" -ForegroundColor Gray
    
    try {
        $params = @{
            Uri = $Url
            Method = $Method
            TimeoutSec = 30
        }
        
        if ($Body) {
            $params.Body = ($Body | ConvertTo-Json -Depth 10)
            $params.ContentType = "application/json"
        }
        
        $response = Invoke-RestMethod @params
        
        Write-Host "✅ 成功" -ForegroundColor Green
        
        if ($Detailed) {
            Write-Host "响应详情:" -ForegroundColor Cyan
            $response | ConvertTo-Json -Depth 10 | Write-Host
        } else {
            # 显示关键信息
            if ($response.status) {
                Write-Host "状态: $($response.status)" -ForegroundColor $(if($response.status -eq "success") {"Green"} else {"Red"})
            }
            
            # 根据不同API显示特定信息
            switch -Regex ($Url) {
                "status$" {
                    if ($response.data.service) {
                        $service = $response.data.service
                        Write-Host "服务: $($service.name) v$($service.version)" -ForegroundColor White
                        Write-Host "状态: $($service.status)" -ForegroundColor White
                        Write-Host "运行时长: $($service.uptime_seconds)秒" -ForegroundColor White
                    }
                    if ($response.data.devices) {
                        $devices = $response.data.devices
                        Write-Host "设备数量: $($devices.count)" -ForegroundColor White
                        Write-Host "设备连接: $($devices.connected)" -ForegroundColor White
                    }
                }
                "validate$" {
                    if ($response.data.validation_summary) {
                        $summary = $response.data.validation_summary
                        Write-Host "总体状态: $($summary.overall_status)" -ForegroundColor $(
                            switch($summary.overall_status) {
                                "success" {"Green"}
                                "warning" {"Yellow"}
                                "error" {"Red"}
                                default {"White"}
                            }
                        )
                        if ($summary.test_counts) {
                            $counts = $summary.test_counts
                            Write-Host "测试结果: 总计$($counts.total), 成功$($counts.success), 警告$($counts.warning), 错误$($counts.error)" -ForegroundColor White
                        }
                    }
                }
                "validate/(ocr|popup)$" {
                    if ($response.data) {
                        $data = $response.data
                        Write-Host "模块: $($data.module)" -ForegroundColor White
                        Write-Host "状态: $($data.status)" -ForegroundColor $(
                            switch($data.status) {
                                "success" {"Green"}
                                "warning" {"Yellow"}
                                "error" {"Red"}
                                default {"White"}
                            }
                        )
                        Write-Host "消息: $($data.message)" -ForegroundColor White
                    }
                }
            }
        }
        
        return $true
    }
    catch {
        Write-Host "❌ 失败" -ForegroundColor Red
        Write-Host "错误: $($_.Exception.Message)" -ForegroundColor Red
        
        # 尝试解析错误响应中的JSON
        if ($_.Exception.Response) {
            try {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorContent = $reader.ReadToEnd()
                $errorJson = $errorContent | ConvertFrom-Json
                
                if ($errorJson.message) {
                    Write-Host "服务器消息: $($errorJson.message)" -ForegroundColor Yellow
                }
                
                if ($Detailed) {
                    Write-Host "错误详情:" -ForegroundColor Cyan
                    $errorJson | ConvertTo-Json -Depth 10 | Write-Host
                }
            }
            catch {
                # 如果无法解析JSON，显示原始错误内容
                if ($errorContent) {
                    Write-Host "原始错误: $errorContent" -ForegroundColor Yellow
                }
            }
        }
        
        return $false
    }
    finally {
        Write-Host "-" * 60 -ForegroundColor Gray
        Write-Host ""
    }
}

# 测试所有API端点
$testResults = @{}

# 1. 测试增强的健康检查API
$testResults["status"] = Test-ApiEndpoint -Url "$BaseUrl/status" -Name "增强的健康检查API"

# 2. 测试综合功能验证API
$testResults["validate"] = Test-ApiEndpoint -Url "$BaseUrl/validate" -Name "综合功能验证API"

# 3. 测试OCR专用验证API
$testResults["validate_ocr"] = Test-ApiEndpoint -Url "$BaseUrl/validate/ocr" -Name "OCR专用验证API"

# 4. 测试弹窗监控专用验证API
$testResults["validate_popup"] = Test-ApiEndpoint -Url "$BaseUrl/validate/popup" -Name "弹窗监控专用验证API"

# 5. 测试POST请求 - OCR验证（带自定义参数）
$ocrBody = @{
    image_path = "C:\temp\test.png"
}
$testResults["validate_ocr_post"] = Test-ApiEndpoint -Url "$BaseUrl/validate/ocr" -Name "OCR验证API (POST)" -Method "POST" -Body $ocrBody

# 6. 测试POST请求 - 综合验证（选择性测试）
$validateBody = @{
    ocr = $true
    popup = $true
}
$testResults["validate_post"] = Test-ApiEndpoint -Url "$BaseUrl/validate" -Name "综合验证API (POST)" -Method "POST" -Body $validateBody

# 显示测试总结
Write-Host "=" * 80 -ForegroundColor Cyan
Write-Host "测试总结" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Cyan

$successCount = ($testResults.Values | Where-Object { $_ -eq $true }).Count
$totalCount = $testResults.Count

Write-Host "总测试数: $totalCount" -ForegroundColor White
Write-Host "成功数: $successCount" -ForegroundColor Green
Write-Host "失败数: $($totalCount - $successCount)" -ForegroundColor Red
Write-Host "成功率: $([math]::Round($successCount / $totalCount * 100, 2))%" -ForegroundColor Yellow

Write-Host ""
Write-Host "详细结果:" -ForegroundColor Cyan
foreach ($test in $testResults.GetEnumerator()) {
    $status = if ($test.Value) { "✅ 成功" } else { "❌ 失败" }
    $color = if ($test.Value) { "Green" } else { "Red" }
    Write-Host "$($test.Key): $status" -ForegroundColor $color
}

Write-Host ""
Write-Host "使用说明:" -ForegroundColor Yellow
Write-Host "- 使用 -Detailed 参数查看完整响应内容" -ForegroundColor Gray
Write-Host "- 使用 -BaseUrl 参数指定不同的API地址" -ForegroundColor Gray
Write-Host "- 示例: .\test_apis.ps1 -Detailed -BaseUrl 'http://localhost:9080/api'" -ForegroundColor Gray

Write-Host ""
Write-Host "测试完成！" -ForegroundColor Green
