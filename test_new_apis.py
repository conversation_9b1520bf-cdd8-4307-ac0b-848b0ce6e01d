#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增API端点的脚本
"""

import requests
import json
import time
from datetime import datetime

# API基础URL
BASE_URL = "http://127.0.0.1:9080/api"

def test_enhanced_status_api():
    """测试增强的状态API"""
    print("=" * 60)
    print("测试增强的状态API: /api/status")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/status", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ API调用成功")
            
            # 检查响应结构
            if "data" in data and "service" in data["data"]:
                service_info = data["data"]["service"]
                print(f"服务名称: {service_info.get('name', 'N/A')}")
                print(f"服务版本: {service_info.get('version', 'N/A')}")
                print(f"服务状态: {service_info.get('status', 'N/A')}")
                print(f"启动时间: {service_info.get('start_time', 'N/A')}")
                print(f"运行时长: {service_info.get('uptime_seconds', 'N/A')}秒")
            
            if "data" in data and "devices" in data["data"]:
                devices_info = data["data"]["devices"]
                print(f"设备数量: {devices_info.get('count', 0)}")
                print(f"设备列表: {devices_info.get('list', [])}")
                print(f"设备连接: {devices_info.get('connected', False)}")
            
            print("✅ 增强状态API测试通过")
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_validation_api():
    """测试功能验证API"""
    print("\n" + "=" * 60)
    print("测试功能验证API: /api/validate")
    print("=" * 60)
    
    try:
        # 测试GET请求
        response = requests.get(f"{BASE_URL}/validate", timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 500]:  # 500也是预期的，因为可能有依赖问题
            data = response.json()
            print("✅ API调用成功")
            
            # 检查响应结构
            if "data" in data and "validation_summary" in data["data"]:
                summary = data["data"]["validation_summary"]
                print(f"总体状态: {summary.get('overall_status', 'N/A')}")
                
                if "test_counts" in summary:
                    counts = summary["test_counts"]
                    print(f"测试总数: {counts.get('total', 0)}")
                    print(f"成功数量: {counts.get('success', 0)}")
                    print(f"警告数量: {counts.get('warning', 0)}")
                    print(f"错误数量: {counts.get('error', 0)}")
            
            # 检查具体验证结果
            if "data" in data and "results" in data["data"]:
                results = data["data"]["results"]
                
                if "ocr" in results:
                    ocr_result = results["ocr"]
                    print(f"OCR验证状态: {ocr_result.get('status', 'N/A')}")
                    print(f"OCR验证消息: {ocr_result.get('message', 'N/A')}")
                
                if "popup_monitor" in results:
                    popup_result = results["popup_monitor"]
                    print(f"弹窗监控验证状态: {popup_result.get('status', 'N/A')}")
                    print(f"弹窗监控验证消息: {popup_result.get('message', 'N/A')}")
            
            print("✅ 功能验证API测试通过")
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_ocr_validation_api():
    """测试OCR专用验证API"""
    print("\n" + "=" * 60)
    print("测试OCR验证API: /api/validate/ocr")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/validate/ocr", timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 500]:
            data = response.json()
            print("✅ API调用成功")
            
            if "data" in data:
                ocr_data = data["data"]
                print(f"OCR模块: {ocr_data.get('module', 'N/A')}")
                print(f"OCR状态: {ocr_data.get('status', 'N/A')}")
                print(f"OCR消息: {ocr_data.get('message', 'N/A')}")
                
                # 检查详细信息
                if "details" in ocr_data:
                    details = ocr_data["details"]
                    print(f"导入状态: {details.get('imports', 'N/A')}")
                    
                    if "dependencies" in details:
                        deps = details["dependencies"]
                        print(f"DLL存在: {deps.get('dll_exists', False)}")
                        print(f"Tessdata存在: {deps.get('tessdata_exists', False)}")
            
            print("✅ OCR验证API测试通过")
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_popup_validation_api():
    """测试弹窗监控专用验证API"""
    print("\n" + "=" * 60)
    print("测试弹窗监控验证API: /api/validate/popup")
    print("=" * 60)
    
    try:
        response = requests.get(f"{BASE_URL}/validate/popup", timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code in [200, 500]:
            data = response.json()
            print("✅ API调用成功")
            
            if "data" in data:
                popup_data = data["data"]
                print(f"弹窗监控模块: {popup_data.get('module', 'N/A')}")
                print(f"弹窗监控状态: {popup_data.get('status', 'N/A')}")
                print(f"弹窗监控消息: {popup_data.get('message', 'N/A')}")
                
                # 检查详细信息
                if "details" in popup_data:
                    details = popup_data["details"]
                    print(f"导入状态: {details.get('imports', 'N/A')}")
                    
                    if "uiautomator2" in details:
                        u2_info = details["uiautomator2"]
                        print(f"uiautomator2可用: {u2_info.get('available', False)}")
                        print(f"uiautomator2版本: {u2_info.get('version', 'N/A')}")
                    
                    if "config" in details:
                        config_info = details["config"]
                        print(f"配置文件存在: {config_info.get('config_exists', False)}")
                        print(f"配置文件有效: {config_info.get('valid_json', False)}")
            
            print("✅ 弹窗监控验证API测试通过")
        else:
            print(f"❌ API调用失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务正在运行")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试新增API端点...")
    print(f"测试时间: {datetime.now().isoformat()}")
    print(f"API基础URL: {BASE_URL}")
    
    # 测试所有新增API
    test_enhanced_status_api()
    test_validation_api()
    test_ocr_validation_api()
    test_popup_validation_api()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    print("注意事项:")
    print("1. 如果出现连接错误，请确保API服务正在运行")
    print("2. 如果验证API返回错误状态，这是正常的，表示依赖检查工作正常")
    print("3. 详细的错误信息可以帮助诊断环境问题")
    print("4. 建议在实际环境中运行API服务后再进行测试")

if __name__ == "__main__":
    main()
