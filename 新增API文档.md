# 新增API端点文档

## 概述

本文档描述了为亮度降幅自动化程序新增的两个RESTful API端点：健康检查API和功能验证API。

**服务地址**: `http://127.0.0.1:9080`  
**API基础路径**: `/api`

## 新增API端点

### 1. 增强的健康检查API

#### `/api/status` - 获取详细系统状态
- **方法**: GET
- **描述**: 获取包含版本信息、启动时间、系统状态等详细信息的健康检查
- **功能**: 
  - 服务基本信息（名称、版本、状态、启动时间、运行时长）
  - 健康状态检查
  - 心跳数据
  - 系统信息（平台、Python版本、内存/CPU使用率）
  - 队列状态
  - 设备连接信息

**响应示例**:
```json
{
  "status": "success",
  "data": {
    "service": {
      "name": "BrightnessReductionAutomation API",
      "version": "3.0.0",
      "status": "running",
      "start_time": "2025-01-20T10:30:00.000Z",
      "uptime_seconds": 3600
    },
    "health": "healthy",
    "heartbeat": {
      "status": "alive",
      "timestamp": "2025-01-20T11:30:00.000Z",
      "uptime": 3600,
      "queue_status": "idle",
      "device_connected": true,
      "api_requests_count": 25
    },
    "system": {
      "platform": "Windows-10",
      "python_version": "3.10.11",
      "memory_usage": "45.2%",
      "cpu_usage": "12.5%"
    },
    "queue": {
      "is_running": false,
      "queue_count": 0,
      "current_task": null
    },
    "devices": {
      "list": ["10AF3F0C1C001NP"],
      "count": 1,
      "connected": true
    },
    "timestamp": "2025-01-20T11:30:00.000Z"
  }
}
```

### 2. 功能验证API

#### `/api/validate` - 综合功能验证
- **方法**: GET, POST
- **描述**: 验证OCR功能和弹窗监控功能是否正常工作
- **功能**:
  - OCR功能测试（依赖检查、文字识别测试）
  - 弹窗监控功能测试（uiautomator2检查、配置验证）
  - 自动生成测试图片或使用自定义图片
  - 详细的验证结果和错误诊断

**参数**:
- `ocr` (可选): 是否验证OCR功能，默认true
- `popup` (可选): 是否验证弹窗监控功能，默认true  
- `image_path` (可选): 自定义测试图片路径

**GET请求示例**:
```
GET /api/validate?ocr=true&popup=true
```

**POST请求示例**:
```json
{
  "ocr": true,
  "popup": true,
  "image_path": "/path/to/test/image.png"
}
```

#### `/api/validate/ocr` - OCR功能专用验证
- **方法**: GET, POST
- **描述**: 仅验证OCR功能
- **功能**:
  - 检查OCR DLL文件和tessdata数据
  - 测试OCR初始化
  - 执行文字识别测试
  - 自动清理临时文件

**参数**:
- `image_path` (可选): 自定义测试图片路径

#### `/api/validate/popup` - 弹窗监控功能专用验证
- **方法**: GET
- **描述**: 仅验证弹窗监控功能
- **功能**:
  - 检查uiautomator2库可用性
  - 验证弹窗配置文件
  - 测试PopupConfig和PopupMonitor初始化
  - 检查监控器状态

## 验证结果说明

### 验证状态
- **success**: 功能验证成功，模块工作正常
- **warning**: 功能部分正常，但可能存在问题
- **error**: 功能验证失败，模块无法正常工作
- **unknown**: 验证状态未知

### OCR验证详情
- **imports**: 模块导入状态
- **dependencies**: DLL和tessdata文件检查
- **test_image**: 测试图片状态（generated/provided）
- **ocr_init**: OCR初始化结果
- **text_recognition**: 文字识别测试结果
- **cleanup**: 临时文件清理状态

### 弹窗监控验证详情
- **imports**: 模块导入状态
- **uiautomator2**: uiautomator2库检查
- **config**: 配置文件检查
- **popup_config_init**: PopupConfig初始化
- **popup_monitor_init**: PopupMonitor初始化
- **monitor_status**: 监控器状态

## 使用示例

### Python示例
```python
import requests

# 增强的健康检查
response = requests.get('http://127.0.0.1:9080/api/status')
status_data = response.json()
print(f"服务状态: {status_data['data']['service']['status']}")
print(f"运行时长: {status_data['data']['service']['uptime_seconds']}秒")

# 综合功能验证
response = requests.get('http://127.0.0.1:9080/api/validate')
validation_data = response.json()
print(f"总体状态: {validation_data['data']['validation_summary']['overall_status']}")

# 仅验证OCR功能
response = requests.get('http://127.0.0.1:9080/api/validate/ocr')
ocr_data = response.json()
print(f"OCR状态: {ocr_data['data']['status']}")
```

### curl示例
```bash
# 增强的健康检查
curl http://127.0.0.1:9080/api/status

# 综合功能验证
curl http://127.0.0.1:9080/api/validate

# 仅验证弹窗监控
curl http://127.0.0.1:9080/api/validate/popup

# 使用自定义图片验证OCR
curl -X POST http://127.0.0.1:9080/api/validate/ocr \
  -H "Content-Type: application/json" \
  -d '{"image_path": "/path/to/test.png"}'
```

## 技术实现

### 代码复用
- 复用现有的`utils/ocr/ButtonCoordinate.py`中的OCR功能
- 复用现有的`utils/PopupMonitor.py`和`utils/PopupConfig.py`
- 复用现有的`api/heartbeat_manager.py`心跳管理功能
- 复用现有的API架构和错误处理机制

### 新增文件
- `api/validation_service.py`: 验证服务核心逻辑
- 扩展了`api/api_server.py`: 添加新的API端点

### 错误处理
- 完整的异常捕获和错误信息记录
- 适当的HTTP状态码返回
- 详细的错误诊断信息
- 自动清理临时文件

## 注意事项

1. **依赖要求**: 
   - OCR功能需要`libtesseract304.dll`和`tessdata`目录
   - 弹窗监控需要`uiautomator2`库和`popup_config.json`

2. **权限要求**: 
   - 需要文件系统读写权限
   - 可能需要网络访问权限（用于设备连接）

3. **性能考虑**:
   - 验证API会创建临时文件，自动清理
   - OCR初始化可能需要几秒钟时间
   - 建议不要频繁调用验证API

4. **兼容性**:
   - 与现有API架构完全兼容
   - 不影响现有功能
   - 遵循现有的编码规范和日志格式
