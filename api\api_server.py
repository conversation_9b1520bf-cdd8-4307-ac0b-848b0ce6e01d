"""
Flask REST API服务器
提供队列管理的REST API接口，端口9080
"""

import threading
import time
from datetime import datetime
from typing import Dict, Any, Optional

try:
    from flask import Flask, jsonify, request
except ImportError:
    print("Flask未安装，请运行: pip install flask")
    Flask = None

from .api_logger import APILogger
from .heartbeat_manager import HeartbeatManager
from .queue_adapter import QueueAdapter


class APIServer:
    """REST API服务器"""
    
    def __init__(self, port: int = 9080, host: str = '127.0.0.1'):
        """
        初始化API服务器
        
        Args:
            port: 服务端口，默认9080
            host: 服务主机，默认127.0.0.1（本地）
        """
        if Flask is None:
            raise ImportError("Flask未安装，无法启动API服务")
        
        self.port = port
        self.host = host
        self.app = Flask(__name__)
        self.is_running = False
        self.server_thread = None
        
        # 初始化组件
        self.logger = APILogger()
        self.heartbeat_manager = HeartbeatManager(interval=5)
        self.queue_adapter = QueueAdapter()
        
        # 设置心跳管理器的队列适配器引用
        self.heartbeat_manager.set_queue_adapter(self.queue_adapter)
        
        # 注册路由
        self._register_routes()
        
        self.logger.info("API服务器初始化完成")
    
    def set_controllers(self, queue_controller=None, main_controller=None, four_controller=None):
        """设置控制器引用"""
        self.queue_adapter.set_controllers(queue_controller, main_controller, four_controller)
        self.logger.info("控制器引用已设置")
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.before_request
        def before_request():
            """请求前处理"""
            self.heartbeat_manager.increment_api_requests()
        
        @self.app.after_request
        def after_request(response):
            """请求后处理"""
            # 记录请求日志
            self.logger.log_request(
                method=request.method,
                endpoint=request.endpoint or request.path,
                params=dict(request.args) if request.args else None,
                response_status=response.status_code
            )
            
            # 添加CORS头
            response.headers['Access-Control-Allow-Origin'] = '*'
            response.headers['Access-Control-Allow-Methods'] = 'GET, POST, DELETE, OPTIONS'
            response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
            
            return response
        
        @self.app.errorhandler(Exception)
        def handle_exception(e):
            """全局异常处理"""
            self.logger.log_error(request.path, e)
            return jsonify({
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }), 500
        
        # 健康检查和心跳
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """健康检查和心跳接口"""
            heartbeat_data = self.heartbeat_manager.get_heartbeat_data()
            system_info = self.heartbeat_manager.get_system_info()
            
            return jsonify({
                "status": "success",
                "data": {
                    "health": "healthy" if self.heartbeat_manager.is_healthy() else "unhealthy",
                    "heartbeat": heartbeat_data,
                    "system": system_info
                }
            })
        
        # 系统状态
        @self.app.route('/api/status', methods=['GET'])
        def get_status():
            """获取系统状态"""
            queue_status = self.queue_adapter.get_queue_status()
            device_list = self.queue_adapter.get_device_list()
            
            return jsonify({
                "status": "success",
                "data": {
                    "queue": queue_status,
                    "devices": device_list,
                    "device_connected": len(device_list) > 0,
                    "timestamp": datetime.now().isoformat()
                }
            })
        
        # 获取队列信息
        @self.app.route('/api/queue', methods=['GET'])
        def get_queue():
            """获取队列状态和列表"""
            queue_status = self.queue_adapter.get_queue_status()
            queue_list = self.queue_adapter.get_queue_list()
            
            return jsonify({
                "status": "success",
                "data": {
                    "queue_status": queue_status,
                    "queue_list": queue_list,
                    "timestamp": datetime.now().isoformat()
                }
            })
        
        # 添加5.0版本队列
        @self.app.route('/api/queue/add/5.0', methods=['POST'])
        def add_queue_5_0():
            """添加5.0版本队列"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({
                        "status": "error",
                        "message": "请求数据为空"
                    }), 400

                apps = data.get('apps')
                preset = data.get('preset')
                auto_brightness = data.get('auto_brightness')

                # 验证参数：必须提供apps或preset中的至少一个
                if not apps and not preset:
                    return jsonify({
                        "status": "error",
                        "message": "必须提供apps参数或preset参数中的至少一个"
                    }), 400

                result = self.queue_adapter.add_queue_5_0(apps, preset, auto_brightness)

                if result.get('success'):
                    return jsonify({
                        "status": "success",
                        "data": result,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        "status": "error",
                        "message": result.get('message', '添加队列失败')
                    }), 400

            except Exception as e:
                self.logger.log_error('/api/queue/add/5.0', e)
                return jsonify({
                    "status": "error",
                    "message": f"添加队列失败: {str(e)}"
                }), 500
        
        # 添加4.0版本队列
        @self.app.route('/api/queue/add/4.0', methods=['POST'])
        def add_queue_4_0():
            """添加4.0版本队列"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({
                        "status": "error",
                        "message": "请求数据为空"
                    }), 400

                apps = data.get('apps')
                brightness_values = data.get('brightness_values')
                preset = data.get('preset')
                auto_brightness = data.get('auto_brightness')

                # 验证参数：必须提供apps或preset中的至少一个，以及brightness_values
                if not apps and not preset:
                    return jsonify({
                        "status": "error",
                        "message": "必须提供apps参数或preset参数中的至少一个"
                    }), 400

                if not brightness_values:
                    return jsonify({
                        "status": "error",
                        "message": "亮度值列表不能为空"
                    }), 400

                result = self.queue_adapter.add_queue_4_0(apps, brightness_values, preset, auto_brightness)

                if result.get('success'):
                    return jsonify({
                        "status": "success",
                        "data": result,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        "status": "error",
                        "message": result.get('message', '添加队列失败')
                    }), 400

            except Exception as e:
                self.logger.log_error('/api/queue/add/4.0', e)
                return jsonify({
                    "status": "error",
                    "message": f"添加队列失败: {str(e)}"
                }), 500

        # 启动队列执行
        @self.app.route('/api/queue/start', methods=['POST'])
        def start_queue():
            """启动队列执行"""
            try:
                data = request.get_json() or {}
                device_id = data.get('device_id')

                result = self.queue_adapter.start_queue_execution(device_id)

                if result.get('success'):
                    return jsonify({
                        "status": "success",
                        "data": result,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        "status": "error",
                        "message": result.get('message', '启动队列失败')
                    }), 400

            except Exception as e:
                self.logger.log_error('/api/queue/start', e)
                return jsonify({
                    "status": "error",
                    "message": f"启动队列失败: {str(e)}"
                }), 500

        # 停止队列执行
        @self.app.route('/api/queue/stop', methods=['POST'])
        def stop_queue():
            """停止队列执行"""
            try:
                result = self.queue_adapter.stop_queue_execution()

                if result.get('success'):
                    return jsonify({
                        "status": "success",
                        "data": result,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        "status": "error",
                        "message": result.get('message', '停止队列失败')
                    }), 400

            except Exception as e:
                self.logger.log_error('/api/queue/stop', e)
                return jsonify({
                    "status": "error",
                    "message": f"停止队列失败: {str(e)}"
                }), 500

        # 清空队列
        @self.app.route('/api/queue/clear', methods=['DELETE'])
        def clear_queue():
            """清空队列"""
            try:
                result = self.queue_adapter.clear_queue()

                if result.get('success'):
                    return jsonify({
                        "status": "success",
                        "data": result,
                        "timestamp": datetime.now().isoformat()
                    })
                else:
                    return jsonify({
                        "status": "error",
                        "message": result.get('message', '清空队列失败')
                    }), 400

            except Exception as e:
                self.logger.log_error('/api/queue/clear', e)
                return jsonify({
                    "status": "error",
                    "message": f"清空队列失败: {str(e)}"
                }), 500

        # 获取设备列表
        @self.app.route('/api/devices', methods=['GET'])
        def get_devices():
            """获取设备列表"""
            try:
                device_list = self.queue_adapter.get_device_list()

                return jsonify({
                    "status": "success",
                    "data": {
                        "devices": device_list,
                        "device_count": len(device_list),
                        "timestamp": datetime.now().isoformat()
                    }
                })

            except Exception as e:
                self.logger.log_error('/api/devices', e)
                return jsonify({
                    "status": "error",
                    "message": f"获取设备列表失败: {str(e)}"
                }), 500

        # 获取预设列表
        @self.app.route('/api/presets', methods=['GET'])
        def get_presets():
            """获取可用的预设列表"""
            try:
                presets = self.queue_adapter.get_available_presets()

                return jsonify({
                    "status": "success",
                    "data": {
                        "presets": presets,
                        "timestamp": datetime.now().isoformat()
                    }
                })

            except Exception as e:
                self.logger.log_error('/api/presets', e)
                return jsonify({
                    "status": "error",
                    "message": f"获取预设列表失败: {str(e)}"
                }), 500

        # 获取执行状态
        @self.app.route('/api/execution/status', methods=['GET'])
        def get_execution_status():
            """获取详细的执行状态信息"""
            try:
                execution_status = self.queue_adapter.get_execution_status()

                if "error" in execution_status:
                    return jsonify({
                        "status": "error",
                        "message": execution_status["error"]
                    }), 500

                return jsonify({
                    "status": "success",
                    "data": execution_status
                })

            except Exception as e:
                self.logger.log_error('/api/execution/status', e)
                return jsonify({
                    "status": "error",
                    "message": f"获取执行状态失败: {str(e)}"
                }), 500

        # 获取执行错误
        @self.app.route('/api/execution/errors', methods=['GET'])
        def get_execution_errors():
            """获取执行错误信息"""
            try:
                error_info = self.queue_adapter.get_execution_errors()

                if "error" in error_info:
                    return jsonify({
                        "status": "error",
                        "message": error_info["error"]
                    }), 500

                return jsonify({
                    "status": "success",
                    "data": error_info
                })

            except Exception as e:
                self.logger.log_error('/api/execution/errors', e)
                return jsonify({
                    "status": "error",
                    "message": f"获取执行错误失败: {str(e)}"
                }), 500

        # 检查执行完成状态
        @self.app.route('/api/execution/completed', methods=['GET'])
        def check_execution_completed():
            """检查执行是否已完成"""
            try:
                is_completed = self.queue_adapter.is_execution_completed()
                is_failed = self.queue_adapter.is_execution_failed()

                return jsonify({
                    "status": "success",
                    "data": {
                        "is_completed": is_completed,
                        "is_failed": is_failed,
                        "timestamp": datetime.now().isoformat()
                    }
                })

            except Exception as e:
                self.logger.log_error('/api/execution/completed', e)
                return jsonify({
                    "status": "error",
                    "message": f"检查完成状态失败: {str(e)}"
                }), 500

        # OPTIONS处理（CORS预检）
        @self.app.route('/api/<path:path>', methods=['OPTIONS'])
        def handle_options(path):
            """处理CORS预检请求"""
            return '', 200

    def start(self):
        """启动API服务器"""
        if self.is_running:
            self.logger.warning("API服务器已在运行中")
            return False

        try:
            # 启动心跳管理器
            self.heartbeat_manager.start()

            # 启动Flask服务器
            self.is_running = True
            self.server_thread = threading.Thread(
                target=self._run_server,
                daemon=True
            )
            self.server_thread.start()

            # 等待服务器启动
            time.sleep(1)

            self.logger.info(f"API服务器已启动 - http://{self.host}:{self.port}")
            print(f"API服务器已启动 - http://{self.host}:{self.port}")
            return True

        except Exception as e:
            self.logger.error(f"启动API服务器失败: {str(e)}")
            print(f"启动API服务器失败: {str(e)}")
            self.is_running = False
            return False

    def stop(self):
        """停止API服务器"""
        if not self.is_running:
            return

        try:
            self.is_running = False

            # 停止心跳管理器
            self.heartbeat_manager.stop()

            # 等待服务器线程结束
            if self.server_thread:
                self.server_thread.join(timeout=2)

            self.logger.info("API服务器已停止")
            print("API服务器已停止")

        except Exception as e:
            self.logger.error(f"停止API服务器失败: {str(e)}")
            print(f"停止API服务器失败: {str(e)}")

    def _run_server(self):
        """运行Flask服务器"""
        try:
            # 禁用Flask的日志输出，使用我们自己的日志系统
            import logging
            log = logging.getLogger('werkzeug')
            log.setLevel(logging.ERROR)

            self.app.run(
                host=self.host,
                port=self.port,
                debug=False,
                use_reloader=False,
                threaded=True
            )
        except Exception as e:
            self.logger.error(f"Flask服务器运行错误: {str(e)}")
            self.is_running = False

    def get_server_info(self) -> Dict[str, Any]:
        """获取服务器信息"""
        return {
            "host": self.host,
            "port": self.port,
            "is_running": self.is_running,
            "heartbeat_interval": self.heartbeat_manager.interval,
            "api_url": f"http://{self.host}:{self.port}/api"
        }
