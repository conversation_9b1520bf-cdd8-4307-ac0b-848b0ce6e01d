import os
import time
from PIL import ImageOps, Image
import sys

from utils.adb.ExAdb import ExAdb
from utils.logs.LoggerManager import get_logger
from utils.ocr import OCR

# from handler.JSONFileHandler import JSONFileHandler as JsonFile
Adb = ExAdb()
logger = get_logger(__name__)


def get_base_path():
    """获取应用运行时的基础路径，兼容源码和打包环境"""
    if getattr(sys, 'frozen', False):
        # 打包后的可执行文件路径
        return os.path.dirname(sys.executable)
    else:
        # 源码运行，返回当前工作目录
        return os.getcwd()


# 图片预处理
def preprocess_image(image_path="../ocr/screenshot.png"):
    logger.info("图片预处理中")
    logger.info("图片路径\n" + image_path)
    # 1. 读取图片
    original_image = Image.open(image_path)

    # 2. 预处理
    # 转换为灰度
    processed_image = original_image.convert('L')

    # 新的步骤 3: 反色处理
    # 实现白变黑，黑变白
    processed_image = ImageOps.invert(processed_image)

    # 新的步骤4: 阈值化处理
    # 设置阈值
    threshold = 180  # 阈值可以根据实际图片进行调整
    # 应用阈值化，将灰色部分的文字更明显
    processed_image = processed_image.point(lambda x: 0 if x < threshold else 255, '1')

    # 可能的附加步骤：去噪、阈值化等
    # 4. 保存图片
    processed_image_path = image_path
    processed_image.save(processed_image_path)

"""获取按钮坐标"""
def click_button_coordinate(text_arr, image_pro=False, device_id=None):
    click_button_coordinate_text(text_arr=text_arr, image_pro=image_pro, device_id=device_id)



# 根据文字去定位
def click_button_coordinate_text(text_arr=None, image_pro=False, device_id=None):
    # 截图，使用ocr确定需要点击的位置
    image_name = "screenshot"
    if device_id:
        image_name = device_id

    base_path = get_base_path()
    local_image_path = os.path.join(base_path, f"{image_name}.png")

    try:
        # 1. 在设备上截图
        if device_id:
            Adb.user_run_adb(f"adb -s {device_id} shell screencap -p /sdcard/{image_name}.png")
        else:
            Adb.user_run_adb(f"adb shell screencap -p /sdcard/{image_name}.png")
        time.sleep(2)

        # 2. 将截图拉取到PC的绝对路径
        if device_id:
            Adb.user_run_adb(f'adb -s {device_id} pull /sdcard/{image_name}.png "{local_image_path}"')
        else:
            Adb.user_run_adb(f'adb pull /sdcard/{image_name}.png "{local_image_path}"')
        
        if image_pro:
            # 图片预处理
            preprocess_image(local_image_path)

        # 运行环境 TODO
        DLL_PATH = './libtesseract304.dll'
        TESSDATA_PREFIX = b'./utils/ocr/tessdata'
        if not os.path.exists(DLL_PATH):
            # 打包环境 TODO
            DLL_PATH = './_internal/libtesseract304.dll'
            TESSDATA_PREFIX = b'./_internal/utils/ocr/tessdata'

        lang = b'chi_sim'
        ocr = OCR.OCR(DLL_PATH, TESSDATA_PREFIX, lang)
        image_file_path = local_image_path
        # 日志记录
        logger.info("OCR插件DLL路径:" + DLL_PATH)
        logger.info("OCR插件data路径:" + str(TESSDATA_PREFIX))
        logger.info("OCR插件lang路径:" + str(lang))
        image_file_path_encoded = image_file_path.encode('utf-8')
        logger.info("图片路径:" + str(image_file_path_encoded))

        # 获取文字
        # result = ocr.get_text(image_file_path)
        # 获取文字和位置
        result_list = ocr.get_text_and_positions(image_file_path_encoded)
        if not result_list:
            # raise Exception("No text found in the image.")
            logger.error("No text found in the image.")
            raise
        for item in result_list:
            if any(substring in item.get("text", "") for substring in text_arr):
                item_arr = item.get("bbox")
                if item_arr is not None:
                    Adb.click_screen(item_arr[0], item_arr[1])
                    logger.info("按钮坐标:" + str(item_arr[0] + "," + item_arr[1]))
                    return
    finally:
        # 删除图片文件
        if os.path.exists(local_image_path):
            try:
                os.remove(local_image_path)
            except OSError as e:
                print(f"Error: {e.strerror} - {e.filename}")
                logger.error("删除图片文件失败:" + str(e))

# ConfigMapper().save_config(data={"name": "1216x2640", "data": json.dumps(ratio)})
# click_button_coordinate_text()